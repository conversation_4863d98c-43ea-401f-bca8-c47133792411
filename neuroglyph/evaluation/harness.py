#!/usr/bin/env python3
"""
NEUROGLYPH Evaluation Harness
Framework centralizzato PyTest-based per benchmark reasoning.
"""

import time
import json
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime, timezone
from abc import ABC, abstractmethod

from .metrics import EvaluationMetrics, BenchmarkResult
from .logger import EvaluationLogger
from .quality_gates import QualityGates


@dataclass
class EvaluationConfig:
    """Configurazione per evaluation harness."""
    
    # Benchmark settings
    benchmark_name: str
    data_path: str
    max_samples: Optional[int] = None
    timeout_per_sample: float = 30.0
    
    # Quality gates
    target_accuracy: float = 0.75
    target_latency_p95: float = 10.0  # secondi
    
    # Output settings
    output_dir: str = "evaluation_results"
    save_detailed_results: bool = True
    generate_html_report: bool = True
    
    # Parallel execution
    max_concurrent: int = 4
    batch_size: int = 10


@dataclass 
class EvaluationSample:
    """Singolo sample di valutazione."""
    
    sample_id: str
    prompt: str
    ground_truth: Any
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Results (populated during evaluation)
    prediction: Optional[str] = None
    is_correct: Optional[bool] = None
    latency: Optional[float] = None
    error_message: Optional[str] = None
    reasoning_trace: Optional[str] = None


class BenchmarkLoader(ABC):
    """Abstract base class per caricamento benchmark."""
    
    @abstractmethod
    def load_samples(self, data_path: str, max_samples: Optional[int] = None) -> List[EvaluationSample]:
        """Carica samples dal dataset."""
        pass
    
    @abstractmethod
    def format_prompt(self, sample: EvaluationSample) -> str:
        """Formatta prompt per il modello."""
        pass
    
    @abstractmethod
    def parse_response(self, response: str, sample: EvaluationSample) -> Any:
        """Parsa risposta del modello."""
        pass
    
    @abstractmethod
    def evaluate_response(self, prediction: Any, ground_truth: Any) -> bool:
        """Valuta correttezza della risposta."""
        pass


class EvaluationHarness:
    """
    Framework centralizzato per evaluation reasoning benchmarks.
    """
    
    def __init__(self, config: EvaluationConfig):
        self.config = config
        self.logger = EvaluationLogger(config.benchmark_name)
        self.metrics = EvaluationMetrics()
        self.quality_gates = QualityGates()
        
        # Setup output directory
        self.output_dir = Path(config.output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Reasoning engine (lazy loaded)
        self._reasoning_engine = None
        
        self.logger.info(f"🚀 EvaluationHarness inizializzato per {config.benchmark_name}")
        self.logger.info(f"📁 Output directory: {self.output_dir}")
        self.logger.info(f"🎯 Target accuracy: {config.target_accuracy:.1%}")
    
    def _get_reasoning_engine(self):
        """Lazy loading del reasoning engine."""
        if self._reasoning_engine is None:
            try:
                from ..cognitive.reasoner import NGReasoner
                self._reasoning_engine = NGReasoner()
                self.logger.info("✅ NGReasoner caricato")
            except ImportError as e:
                self.logger.error(f"❌ Impossibile caricare NGReasoner: {e}")
                raise
        return self._reasoning_engine
    
    async def evaluate_benchmark(
        self, 
        loader: BenchmarkLoader,
        model_name: str = "neuroglyph"
    ) -> BenchmarkResult:
        """
        Esegue valutazione completa su un benchmark.
        
        Args:
            loader: Loader specifico per il benchmark
            model_name: Nome del modello da valutare
            
        Returns:
            BenchmarkResult con metriche complete
        """
        start_time = time.time()
        
        self.logger.info(f"🔬 Iniziando valutazione {self.config.benchmark_name}")
        
        # Carica samples
        samples = loader.load_samples(self.config.data_path, self.config.max_samples)
        self.logger.info(f"📊 Caricati {len(samples)} samples")
        
        # Esegui valutazione in batch
        evaluated_samples = await self._evaluate_samples_batch(samples, loader)
        
        # Calcola metriche
        result = self._compute_benchmark_result(
            evaluated_samples, 
            model_name,
            time.time() - start_time
        )
        
        # Salva risultati
        await self._save_results(result, evaluated_samples)
        
        # Verifica quality gates
        gates_passed = self.quality_gates.check_gates(result, self.config)
        
        self.logger.info(f"✅ Valutazione completata in {result.total_time:.2f}s")
        self.logger.info(f"📊 Accuracy: {result.accuracy:.1%}")
        self.logger.info(f"🎯 Quality gates: {'✅ PASSED' if gates_passed else '❌ FAILED'}")
        
        return result
    
    async def _evaluate_samples_batch(
        self, 
        samples: List[EvaluationSample], 
        loader: BenchmarkLoader
    ) -> List[EvaluationSample]:
        """
        Valuta samples in batch con parallelizzazione.
        """
        semaphore = asyncio.Semaphore(self.config.max_concurrent)
        
        async def evaluate_single(sample: EvaluationSample) -> EvaluationSample:
            async with semaphore:
                return await self._evaluate_single_sample(sample, loader)
        
        # Esegui in batch
        tasks = []
        for i in range(0, len(samples), self.config.batch_size):
            batch = samples[i:i + self.config.batch_size]
            batch_tasks = [evaluate_single(sample) for sample in batch]
            tasks.extend(batch_tasks)
        
        # Attendi completamento con progress
        evaluated = []
        for i, task in enumerate(asyncio.as_completed(tasks)):
            result = await task
            evaluated.append(result)
            
            if (i + 1) % 10 == 0:
                self.logger.info(f"📈 Progress: {i + 1}/{len(tasks)} samples")
        
        return evaluated
    
    async def _evaluate_single_sample(
        self, 
        sample: EvaluationSample, 
        loader: BenchmarkLoader
    ) -> EvaluationSample:
        """
        Valuta un singolo sample.
        """
        start_time = time.time()
        
        try:
            # Format prompt
            formatted_prompt = loader.format_prompt(sample)
            
            # Get reasoning engine
            reasoner = self._get_reasoning_engine()
            
            # Execute reasoning with timeout
            response = await asyncio.wait_for(
                self._execute_reasoning(reasoner, formatted_prompt),
                timeout=self.config.timeout_per_sample
            )
            
            # Parse response
            prediction = loader.parse_response(response, sample)
            
            # Evaluate correctness
            is_correct = loader.evaluate_response(prediction, sample.ground_truth)
            
            # Update sample
            sample.prediction = str(prediction)
            sample.is_correct = is_correct
            sample.latency = time.time() - start_time
            sample.reasoning_trace = response
            
        except asyncio.TimeoutError:
            sample.error_message = f"Timeout after {self.config.timeout_per_sample}s"
            sample.is_correct = False
            sample.latency = self.config.timeout_per_sample
            
        except Exception as e:
            sample.error_message = str(e)
            sample.is_correct = False
            sample.latency = time.time() - start_time
            
            self.logger.warning(f"⚠️ Error evaluating sample {sample.sample_id}: {e}")
        
        return sample
    
    async def _execute_reasoning(self, reasoner, prompt: str) -> str:
        """
        Esegue reasoning su un prompt.
        """
        # Per ora implementazione semplificata
        # In futuro: integrazione completa con NGReasoner
        try:
            # Simula reasoning asincrono
            await asyncio.sleep(0.1)  # Simula processing time
            
            # Placeholder response
            return f"Reasoning result for: {prompt[:100]}..."
            
        except Exception as e:
            raise RuntimeError(f"Reasoning failed: {e}")
    
    def _compute_benchmark_result(
        self, 
        samples: List[EvaluationSample], 
        model_name: str,
        total_time: float
    ) -> BenchmarkResult:
        """
        Computa metriche finali del benchmark.
        """
        # Filtra samples validi
        valid_samples = [s for s in samples if s.is_correct is not None]
        correct_samples = [s for s in valid_samples if s.is_correct]
        
        # Calcola metriche base
        accuracy = len(correct_samples) / len(valid_samples) if valid_samples else 0.0
        
        # Latency metrics
        latencies = [s.latency for s in valid_samples if s.latency is not None]
        avg_latency = sum(latencies) / len(latencies) if latencies else 0.0
        p95_latency = sorted(latencies)[int(0.95 * len(latencies))] if latencies else 0.0
        
        # Error analysis
        error_types = {}
        for sample in samples:
            if sample.error_message:
                error_type = sample.error_message.split(':')[0]
                error_types[error_type] = error_types.get(error_type, 0) + 1
        
        return BenchmarkResult(
            benchmark_name=self.config.benchmark_name,
            model_name=model_name,
            total_samples=len(samples),
            valid_samples=len(valid_samples),
            correct_samples=len(correct_samples),
            accuracy=accuracy,
            avg_latency=avg_latency,
            p95_latency=p95_latency,
            total_time=total_time,
            error_analysis=error_types,
            timestamp=datetime.now(timezone.utc)
        )
    
    async def _save_results(
        self, 
        result: BenchmarkResult, 
        samples: List[EvaluationSample]
    ):
        """
        Salva risultati su disco.
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Salva summary JSON
        summary_path = self.output_dir / f"{self.config.benchmark_name}_{timestamp}_summary.json"
        with open(summary_path, 'w') as f:
            json.dump(result.__dict__, f, indent=2, default=str)
        
        # Salva detailed results se richiesto
        if self.config.save_detailed_results:
            detailed_path = self.output_dir / f"{self.config.benchmark_name}_{timestamp}_detailed.json"
            detailed_data = {
                'config': self.config.__dict__,
                'result': result.__dict__,
                'samples': [sample.__dict__ for sample in samples]
            }
            with open(detailed_path, 'w') as f:
                json.dump(detailed_data, f, indent=2, default=str)
        
        # Genera HTML report se richiesto
        if self.config.generate_html_report:
            await self._generate_html_report(result, samples, timestamp)
        
        self.logger.info(f"📄 Risultati salvati in {self.output_dir}")
    
    async def _generate_html_report(
        self, 
        result: BenchmarkResult, 
        samples: List[EvaluationSample],
        timestamp: str
    ):
        """
        Genera report HTML con grafici.
        """
        # Implementazione semplificata per ora
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>NEUROGLYPH Evaluation Report - {result.benchmark_name}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .metric {{ background: #f5f5f5; padding: 20px; margin: 10px 0; border-radius: 5px; }}
                .success {{ color: green; }}
                .failure {{ color: red; }}
            </style>
        </head>
        <body>
            <h1>🧠 NEUROGLYPH Evaluation Report</h1>
            <h2>📊 {result.benchmark_name} - {result.model_name}</h2>
            
            <div class="metric">
                <h3>🎯 Accuracy</h3>
                <p class="{'success' if result.accuracy >= self.config.target_accuracy else 'failure'}">
                    {result.accuracy:.1%} ({result.correct_samples}/{result.valid_samples})
                </p>
            </div>
            
            <div class="metric">
                <h3>⏱️ Performance</h3>
                <p>Average Latency: {result.avg_latency:.2f}s</p>
                <p>P95 Latency: {result.p95_latency:.2f}s</p>
                <p>Total Time: {result.total_time:.2f}s</p>
            </div>
            
            <div class="metric">
                <h3>❌ Error Analysis</h3>
                <ul>
                    {''.join(f'<li>{error}: {count}</li>' for error, count in result.error_analysis.items())}
                </ul>
            </div>
            
            <p><small>Generated: {result.timestamp}</small></p>
        </body>
        </html>
        """
        
        html_path = self.output_dir / f"{self.config.benchmark_name}_{timestamp}_report.html"
        with open(html_path, 'w') as f:
            f.write(html_content)
        
        self.logger.info(f"📊 HTML report generato: {html_path}")
